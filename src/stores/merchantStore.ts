import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { Merchant } from '../types';
import { MerchantStatus } from '../types';

// 商户状态接口
interface MerchantState {
  // 状态
  merchants: Merchant[];
  selectedMerchant: Merchant | null;
  isLoading: boolean;
  error: string | null;
  filters: {
    status?: MerchantStatus;
    keyword?: string;
  };

  // 操作
  setMerchants: (merchants: Merchant[]) => void;
  addMerchant: (merchant: Merchant) => void;
  updateMerchant: (id: string, updates: Partial<Merchant>) => void;
  deleteMerchant: (id: string) => void;
  selectMerchant: (merchant: Merchant | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setFilters: (filters: Partial<MerchantState['filters']>) => void;
  clearFilters: () => void;
}

// 商户状态管理（使用 subscribeWithSelector 中间件支持订阅）
export const useMerchantStore = create<MerchantState>()(
  subscribeWithSelector(
    immer(set => ({
      // 初始状态
      merchants: [],
      selectedMerchant: null,
      isLoading: false,
      error: null,
      filters: {},

      // 设置商户列表
      setMerchants: (merchants: Merchant[]) => {
        set(state => {
          state.merchants = merchants;
          state.isLoading = false;
          state.error = null;
        });
      },

      // 添加商户
      addMerchant: (merchant: Merchant) => {
        set(state => {
          state.merchants.push(merchant);
        });
      },

      // 更新商户
      updateMerchant: (id: string, updates: Partial<Merchant>) => {
        set(state => {
          const index = state.merchants.findIndex(m => m.id === id);
          if (index !== -1) {
            Object.assign(state.merchants[index], updates);
          }

          // 如果更新的是当前选中的商户，也要更新选中状态
          if (state.selectedMerchant?.id === id) {
            Object.assign(state.selectedMerchant, updates);
          }
        });
      },

      // 删除商户
      deleteMerchant: (id: string) => {
        set(state => {
          state.merchants = state.merchants.filter(m => m.id !== id);

          // 如果删除的是当前选中的商户，清空选中状态
          if (state.selectedMerchant?.id === id) {
            state.selectedMerchant = null;
          }
        });
      },

      // 选择商户
      selectMerchant: (merchant: Merchant | null) => {
        set(state => {
          state.selectedMerchant = merchant;
        });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set(state => {
          state.isLoading = loading;
          if (loading) {
            state.error = null;
          }
        });
      },

      // 设置错误
      setError: (error: string | null) => {
        set(state => {
          state.error = error;
          state.isLoading = false;
        });
      },

      // 设置筛选条件
      setFilters: (filters: Partial<MerchantState['filters']>) => {
        set(state => {
          Object.assign(state.filters, filters);
        });
      },

      // 清空筛选条件
      clearFilters: () => {
        set(state => {
          state.filters = {};
        });
      },
    }))
  )
);

// 选择器函数
export const useMerchants = () => useMerchantStore(state => state.merchants);
export const useSelectedMerchant = () => useMerchantStore(state => state.selectedMerchant);
export const useMerchantLoading = () => useMerchantStore(state => state.isLoading);
export const useMerchantError = () => useMerchantStore(state => state.error);
export const useMerchantFilters = () => useMerchantStore(state => state.filters);

// 注意：计算属性选择器已移至组件级别使用 useMemo 来避免无限循环
// 如果需要在多个组件中使用，建议在组件级别实现或使用专门的计算库

// 操作选择器 - 标准 Zustand 模式
export const useMerchantActions = () => ({
  setMerchants: useMerchantStore.getState().setMerchants,
  addMerchant: useMerchantStore.getState().addMerchant,
  updateMerchant: useMerchantStore.getState().updateMerchant,
  deleteMerchant: useMerchantStore.getState().deleteMerchant,
  selectMerchant: useMerchantStore.getState().selectMerchant,
  setLoading: useMerchantStore.getState().setLoading,
  setError: useMerchantStore.getState().setError,
  setFilters: useMerchantStore.getState().setFilters,
  clearFilters: useMerchantStore.getState().clearFilters,
});

// 订阅商户变化的示例
export function subscribeMerchantChanges() {
  return useMerchantStore.subscribe(
    state => state.merchants,
    (merchants, prevMerchants) => {
      console.log('商户列表发生变化:', {
        previous: prevMerchants.length,
        current: merchants.length,
      });
    }
  );
}
